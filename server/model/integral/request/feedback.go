package request

import (
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/integral"
)

// FeedbackSearch 反馈搜索条件
type FeedbackSearch struct {
	integral.Feedback
	Title          string  `json:"title" form:"title"`                   // 反馈标题
	StartCreatedAt *string `json:"startCreatedAt" form:"startCreatedAt"` // 创建时间-开始
	EndCreatedAt   *string `json:"endCreatedAt" form:"endCreatedAt"`     // 创建时间-结束
	request.PageInfo
}

// FeedbackTaskListRequest 反馈任务列表请求
type FeedbackTaskListRequest struct {
	request.PageInfo
}

// CreateFeedbackRequest 创建反馈请求
type CreateFeedbackRequest struct {
	ProjectsID *uint  `json:"projects_id" label:"关联的MCP项目ID"`                                   // 关联的MCP项目ID（非必传）
	Title      string `json:"title" label:"反馈标题"`                                                // 反馈标题
	Content    string `json:"content" binding:"required,min=10" label:"反馈内容"`                    // 反馈内容
	Type       string `json:"type" binding:"required,oneof=suggestion bug feature" label:"反馈类型"` // 反馈类型
	Tags       string `json:"tags" label:"标签"`                                                     // 标签
}

// UpdateFeedbackRequest 更新反馈请求
type UpdateFeedbackRequest struct {
	ID      uint   `json:"id" binding:"required" label:"反馈ID"`                                  // 反馈ID
	Title   string `json:"title"  label:"反馈标题"`                                               // 反馈标题
	Content string `json:"content" binding:"required,min=10" label:"反馈内容"`                    // 反馈内容
	Type    string `json:"type" binding:"required,oneof=suggestion bug feature" label:"反馈类型"` // 反馈类型
	Tags    string `json:"tags" label:"标签"`                                                     // 标签
}

// ReviewFeedbackRequest 审核反馈请求
type ReviewFeedbackRequest struct {
	ID         uint   `json:"id" binding:"required" label:"反馈ID"`                               // 反馈ID
	Status     string `json:"status" binding:"required,oneof=approved rejected" label:"审核状态"` // 审核状态
	IsStarred  bool   `json:"is_starred" label:"是否标星"`                                        // 是否标星
	IsPublic   bool   `json:"is_public" label:"是否公开"`                                         // 是否公开
	AdminReply string `json:"admin_reply" label:"管理员回复"`                                     // 管理员回复
	Priority   int    `json:"priority" label:"优先级"`                                            // 优先级
	Remark     string `json:"remark" label:"备注"`                                                // 备注
}

// LikeFeedbackRequest 点赞反馈请求
type LikeFeedbackRequest struct {
	ID uint `json:"id" binding:"required" label:"反馈ID"` // 反馈ID
}
