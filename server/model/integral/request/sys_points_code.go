package request

import (
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
)

// CreatePointsCodeRequest 创建积分兑换码请求
type CreatePointsCodeRequest struct {
	Points      int        `json:"points" binding:"required,min=1" label:"积分数量"`        // 积分数量
	Description string     `json:"description" binding:"max=200" label:"描述"`           // 描述
	MaxUses     int        `json:"max_uses" binding:"min=0" label:"最大使用次数"`            // 最大使用次数，0表示无限制
	ExpiresAt   *time.Time `json:"expires_at" label:"过期时间"`                           // 过期时间
	Remark      string     `json:"remark" binding:"max=500" label:"备注"`               // 备注
}

// BatchCreatePointsCodeRequest 批量创建积分兑换码请求
type BatchCreatePointsCodeRequest struct {
	Points      int        `json:"points" binding:"required,min=1" label:"积分数量"`        // 积分数量
	Description string     `json:"description" binding:"max=200" label:"描述"`           // 描述
	MaxUses     int        `json:"max_uses" binding:"min=0" label:"最大使用次数"`            // 最大使用次数，0表示无限制
	ExpiresAt   *time.Time `json:"expires_at" label:"过期时间"`                           // 过期时间
	Count       int        `json:"count" binding:"required,min=1,max=1000" label:"生成数量"` // 生成数量，最多1000个
	Remark      string     `json:"remark" binding:"max=500" label:"备注"`               // 备注
}

// UpdatePointsCodeRequest 更新积分兑换码请求
type UpdatePointsCodeRequest struct {
	ID          uint       `json:"id" binding:"required" label:"兑换码ID"`               // 兑换码ID
	Points      int        `json:"points" binding:"required,min=1" label:"积分数量"`        // 积分数量
	Description string     `json:"description" binding:"max=200" label:"描述"`           // 描述
	MaxUses     int        `json:"max_uses" binding:"min=0" label:"最大使用次数"`            // 最大使用次数
	ExpiresAt   *time.Time `json:"expires_at" label:"过期时间"`                           // 过期时间
	IsActive    bool       `json:"is_active" label:"是否激活"`                            // 是否激活
	Remark      string     `json:"remark" binding:"max=500" label:"备注"`               // 备注
}

// PointsCodeSearch 积分兑换码查询参数
type PointsCodeSearch struct {
	request.PageInfo
	Code        string `json:"code" form:"code" label:"兑换码"`                    // 兑换码
	BatchID     string `json:"batch_id" form:"batch_id" label:"批次ID"`          // 批次ID
	IsActive    *bool  `json:"is_active" form:"is_active" label:"是否激活"`        // 是否激活
	CreatedBy   uint   `json:"created_by" form:"created_by" label:"创建者ID"`     // 创建者ID
	StartDate   string `json:"start_date" form:"start_date" label:"开始日期"`      // 开始日期
	EndDate     string `json:"end_date" form:"end_date" label:"结束日期"`          // 结束日期
	Status      string `json:"status" form:"status" label:"状态"`                // 状态：active/inactive/expired/exhausted
}

// UsePointsCodeRequest 使用积分兑换码请求
type UsePointsCodeRequest struct {
	Code string `json:"code" binding:"required,min=6,max=50" label:"兑换码"` // 兑换码
}

// PointsCodeUsageSearch 兑换码使用记录查询参数
type PointsCodeUsageSearch struct {
	request.PageInfo
	CodeID    uint   `json:"code_id" form:"code_id" label:"兑换码ID"`       // 兑换码ID
	UserID    uint   `json:"user_id" form:"user_id" label:"用户ID"`        // 用户ID
	StartDate string `json:"start_date" form:"start_date" label:"开始日期"` // 开始日期
	EndDate   string `json:"end_date" form:"end_date" label:"结束日期"`     // 结束日期
}
