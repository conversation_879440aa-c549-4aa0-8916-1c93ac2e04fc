package integral

import (
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
)

// SysPointsCode 积分兑换码表
type SysPointsCode struct {
	global.GVA_MODEL
	Code        string     `json:"code" gorm:"uniqueIndex;not null;comment:兑换码"`                    // 兑换码（唯一）
	Points      int        `json:"points" gorm:"not null;comment:积分数量"`                             // 积分数量
	Description string     `json:"description" gorm:"comment:描述"`                                   // 描述
	MaxUses     int        `json:"max_uses" gorm:"default:1;comment:最大使用次数"`                        // 最大使用次数，0表示无限制
	UsedCount   int        `json:"used_count" gorm:"default:0;comment:已使用次数"`                       // 已使用次数
	ExpiresAt   *time.Time `json:"expires_at" gorm:"comment:过期时间"`                                  // 过期时间，null表示永不过期
	IsActive    bool       `json:"is_active" gorm:"default:true;comment:是否激活"`                      // 是否激活
	CreatedBy   uint       `json:"created_by" gorm:"comment:创建者ID"`                                 // 创建者ID（管理员）
	BatchID     string     `json:"batch_id" gorm:"index;comment:批次ID"`                             // 批次ID，用于批量生成
	Remark      string     `json:"remark" gorm:"comment:备注"`                                        // 备注
}

func (SysPointsCode) TableName() string {
	return "sys_points_codes"
}

// SysPointsCodeUsage 积分兑换码使用记录表
type SysPointsCodeUsage struct {
	global.GVA_MODEL
	CodeID    uint      `json:"code_id" gorm:"index;not null;comment:兑换码ID"`     // 兑换码ID
	UserID    uint      `json:"user_id" gorm:"index;not null;comment:用户ID"`      // 使用者ID
	Points    int       `json:"points" gorm:"not null;comment:获得积分"`             // 获得积分
	UsedAt    time.Time `json:"used_at" gorm:"not null;comment:使用时间"`            // 使用时间
	IPAddress string    `json:"ip_address" gorm:"comment:使用者IP"`                 // 使用者IP
	UserAgent string    `json:"user_agent" gorm:"comment:用户代理"`                 // 用户代理
}

func (SysPointsCodeUsage) TableName() string {
	return "sys_points_code_usage"
}

// 兑换码状态常量
const (
	PointsCodeStatusActive   = "active"   // 激活状态
	PointsCodeStatusInactive = "inactive" // 未激活状态
	PointsCodeStatusExpired  = "expired"  // 已过期
	PointsCodeStatusExhausted = "exhausted" // 已用完
)

// IsExpired 检查兑换码是否过期
func (p *SysPointsCode) IsExpired() bool {
	if p.ExpiresAt == nil {
		return false
	}
	return time.Now().After(*p.ExpiresAt)
}

// IsExhausted 检查兑换码是否已用完
func (p *SysPointsCode) IsExhausted() bool {
	if p.MaxUses == 0 {
		return false // 无限制使用
	}
	return p.UsedCount >= p.MaxUses
}

// CanUse 检查兑换码是否可以使用
func (p *SysPointsCode) CanUse() bool {
	return p.IsActive && !p.IsExpired() && !p.IsExhausted()
}

// GetStatus 获取兑换码状态
func (p *SysPointsCode) GetStatus() string {
	if !p.IsActive {
		return PointsCodeStatusInactive
	}
	if p.IsExpired() {
		return PointsCodeStatusExpired
	}
	if p.IsExhausted() {
		return PointsCodeStatusExhausted
	}
	return PointsCodeStatusActive
}
