package integral

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/system"
)

// Feedback 用户反馈表
// 用于存储用户反馈意见和建议
type Feedback struct {
	global.GVA_MODEL
	UserID      uint   `json:"user_id" gorm:"not null;comment:用户ID"`                           // 用户ID
	ProjectsID  *uint  `json:"projects_id" gorm:"comment:关联的MCP项目ID"`                       // 关联的MCP项目ID
	Title       string `json:"title" gorm:"type:varchar(200);comment:反馈标题"`                  // 反馈标题
	Content     string `json:"content" gorm:"type:text;not null;comment:反馈内容"`               // 反馈内容
	Type        string `json:"type" gorm:"type:varchar(20);default:suggestion;comment:反馈类型"` // 反馈类型：suggestion(建议), bug(问题反馈), feature(功能需求)
	Status      string `json:"status" gorm:"type:varchar(20);default:pending;comment:审核状态"`  // 审核状态：pending(待审核), approved(已通过), rejected(已拒绝)
	IsStarred   bool   `json:"is_starred" gorm:"default:false;comment:是否标星"`                 // 是否标星
	IsPublic    bool   `json:"is_public" gorm:"default:false;comment:是否公开显示"`              // 是否公开显示
	AdminReply  string `json:"admin_reply" gorm:"type:text;comment:管理员回复"`                  // 管理员回复
	AdminUserID uint   `json:"admin_user_id" gorm:"comment:审核管理员ID"`                        // 审核管理员ID
	ViewCount   int    `json:"view_count" gorm:"default:0;comment:查看次数"`                     // 查看次数
	LikeCount   int    `json:"like_count" gorm:"default:0;comment:点赞次数"`                     // 点赞次数
	Tags        string `json:"tags" gorm:"comment:标签，多个标签用逗号分隔"`                      // 标签
	Priority    int    `json:"priority" gorm:"default:0;comment:优先级，数字越大优先级越高"`      // 优先级
	Remark      string `json:"remark" gorm:"comment:备注"`                                       // 备注

	// 关联用户信息
	User system.SysUser `json:"user" gorm:"foreignKey:UserID;references:ID"` // 关联用户

	// 关联项目信息（不存储在数据库中）
	ProjectName string `json:"project_name" gorm:"-"` // 项目名称

	// 关联用户信息（不存储在数据库中）
	UserName   string `json:"user_name" gorm:"-"`   // 用户名
	UserAvatar string `json:"user_avatar" gorm:"-"` // 用户头像
	AdminName  string `json:"admin_name" gorm:"-"`  // 管理员名称
}

func (Feedback) TableName() string {
	return "feedbacks"
}

// 反馈类型常量
const (
	FeedbackTypeSuggestion = "suggestion" // 建议
	FeedbackTypeBug        = "bug"        // 问题反馈
	FeedbackTypeFeature    = "feature"    // 功能需求
)

// 审核状态常量
const (
	FeedbackStatusPending  = "pending"  // 待审核
	FeedbackStatusApproved = "approved" // 已通过
	FeedbackStatusRejected = "rejected" // 已拒绝
)
