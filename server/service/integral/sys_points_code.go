package integral

import (
	"crypto/rand"
	"fmt"
	"math/big"
	"strings"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/config"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/integral"
	integralReq "github.com/flipped-aurora/gin-vue-admin/server/model/integral/request"
	integralRes "github.com/flipped-aurora/gin-vue-admin/server/model/integral/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/system"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

type PointsCodeService struct{}

var PointsCodeServiceApp = new(PointsCodeService)

// generateCode 生成兑换码
func (s *PointsCodeService) generateCode(length int) (string, error) {
	const charset = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	code := make([]byte, length)
	for i := range code {
		num, err := rand.Int(rand.Reader, big.NewInt(int64(len(charset))))
		if err != nil {
			return "", err
		}
		code[i] = charset[num.Int64()]
	}
	return string(code), nil
}

// generateUniqueCode 生成唯一兑换码
func (s *PointsCodeService) generateUniqueCode() (string, error) {
	maxAttempts := 10
	for i := 0; i < maxAttempts; i++ {
		code, err := s.generateCode(12) // 生成12位兑换码
		if err != nil {
			return "", err
		}
		
		// 检查是否已存在
		var count int64
		err = global.GVA_DB.Model(&integral.SysPointsCode{}).Where("code = ?", code).Count(&count).Error
		if err != nil {
			return "", err
		}
		
		if count == 0 {
			return code, nil
		}
	}
	return "", fmt.Errorf("生成唯一兑换码失败，请重试")
}

// CreatePointsCode 创建积分兑换码
func (s *PointsCodeService) CreatePointsCode(req integralReq.CreatePointsCodeRequest, createdBy uint) (integral.SysPointsCode, error) {
	code, err := s.generateUniqueCode()
	if err != nil {
		return integral.SysPointsCode{}, err
	}

	pointsCode := integral.SysPointsCode{
		Code:        code,
		Points:      req.Points,
		Description: req.Description,
		MaxUses:     req.MaxUses,
		ExpiresAt:   req.ExpiresAt,
		IsActive:    true,
		CreatedBy:   createdBy,
		BatchID:     uuid.New().String(),
		Remark:      req.Remark,
	}

	err = global.GVA_DB.Create(&pointsCode).Error
	return pointsCode, err
}

// BatchCreatePointsCode 批量创建积分兑换码
func (s *PointsCodeService) BatchCreatePointsCode(req integralReq.BatchCreatePointsCodeRequest, createdBy uint) (integralRes.BatchCreatePointsCodeResponse, error) {
	batchID := uuid.New().String()
	var codes []string
	var pointsCodes []integral.SysPointsCode

	// 批量生成兑换码
	for i := 0; i < req.Count; i++ {
		code, err := s.generateUniqueCode()
		if err != nil {
			return integralRes.BatchCreatePointsCodeResponse{}, fmt.Errorf("生成第%d个兑换码失败: %v", i+1, err)
		}

		pointsCode := integral.SysPointsCode{
			Code:        code,
			Points:      req.Points,
			Description: req.Description,
			MaxUses:     req.MaxUses,
			ExpiresAt:   req.ExpiresAt,
			IsActive:    true,
			CreatedBy:   createdBy,
			BatchID:     batchID,
			Remark:      req.Remark,
		}

		codes = append(codes, code)
		pointsCodes = append(pointsCodes, pointsCode)
	}

	// 批量插入数据库
	err := global.GVA_DB.CreateInBatches(pointsCodes, 100).Error
	if err != nil {
		return integralRes.BatchCreatePointsCodeResponse{}, err
	}

	return integralRes.BatchCreatePointsCodeResponse{
		BatchID:     batchID,
		Count:       req.Count,
		Codes:       codes,
		Points:      req.Points,
		Description: req.Description,
		ExpiresAt:   req.ExpiresAt,
	}, nil
}

// GetPointsCodeList 获取积分兑换码列表
func (s *PointsCodeService) GetPointsCodeList(req integralReq.PointsCodeSearch) (integralRes.PointsCodeListResponse, error) {
	limit := req.PageSize
	offset := req.PageSize * (req.Page - 1)

	db := global.GVA_DB.Model(&integral.SysPointsCode{})

	// 添加查询条件
	if req.Code != "" {
		db = db.Where("code LIKE ?", "%"+req.Code+"%")
	}
	if req.BatchID != "" {
		db = db.Where("batch_id = ?", req.BatchID)
	}
	if req.IsActive != nil {
		db = db.Where("is_active = ?", *req.IsActive)
	}
	if req.CreatedBy != 0 {
		db = db.Where("created_by = ?", req.CreatedBy)
	}
	if req.StartDate != "" {
		db = db.Where("created_at >= ?", req.StartDate)
	}
	if req.EndDate != "" {
		db = db.Where("created_at <= ?", req.EndDate+" 23:59:59")
	}

	// 状态过滤
	if req.Status != "" {
		switch req.Status {
		case integral.PointsCodeStatusActive:
			db = db.Where("is_active = ? AND (expires_at IS NULL OR expires_at > ?) AND (max_uses = 0 OR used_count < max_uses)", true, time.Now())
		case integral.PointsCodeStatusInactive:
			db = db.Where("is_active = ?", false)
		case integral.PointsCodeStatusExpired:
			db = db.Where("expires_at IS NOT NULL AND expires_at <= ?", time.Now())
		case integral.PointsCodeStatusExhausted:
			db = db.Where("max_uses > 0 AND used_count >= max_uses")
		}
	}

	// 获取总数
	var total int64
	err := db.Count(&total).Error
	if err != nil {
		return integralRes.PointsCodeListResponse{}, err
	}

	// 获取列表
	var pointsCodes []integral.SysPointsCode
	err = db.Order("created_at DESC").Limit(limit).Offset(offset).Find(&pointsCodes).Error
	if err != nil {
		return integralRes.PointsCodeListResponse{}, err
	}

	// 获取创建者信息
	var createdByIDs []uint
	for _, code := range pointsCodes {
		createdByIDs = append(createdByIDs, code.CreatedBy)
	}

	var users []system.SysUser
	userMap := make(map[uint]string)
	if len(createdByIDs) > 0 {
		global.GVA_DB.Where("id IN ?", createdByIDs).Find(&users)
		for _, user := range users {
			userMap[user.ID] = user.NickName
		}
	}

	// 转换为响应格式
	var list []integralRes.PointsCodeResponse
	for _, code := range pointsCodes {
		createdByName := userMap[code.CreatedBy]
		if createdByName == "" {
			createdByName = "未知"
		}
		list = append(list, integralRes.ConvertToPointsCodeResponse(code, createdByName))
	}

	return integralRes.PointsCodeListResponse{
		List:     list,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}, nil
}

// UpdatePointsCode 更新积分兑换码
func (s *PointsCodeService) UpdatePointsCode(req integralReq.UpdatePointsCodeRequest) error {
	updates := map[string]interface{}{
		"points":      req.Points,
		"description": req.Description,
		"max_uses":    req.MaxUses,
		"expires_at":  req.ExpiresAt,
		"is_active":   req.IsActive,
		"remark":      req.Remark,
	}

	return global.GVA_DB.Model(&integral.SysPointsCode{}).Where("id = ?", req.ID).Updates(updates).Error
}

// DeletePointsCode 删除积分兑换码
func (s *PointsCodeService) DeletePointsCode(id uint) error {
	return global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		// 删除使用记录
		if err := tx.Where("code_id = ?", id).Delete(&integral.SysPointsCodeUsage{}).Error; err != nil {
			return err
		}
		// 删除兑换码
		return tx.Delete(&integral.SysPointsCode{}, id).Error
	})
}

// DeletePointsCodeBatch 批量删除积分兑换码
func (s *PointsCodeService) DeletePointsCodeBatch(batchID string) error {
	return global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		// 获取批次中的所有兑换码ID
		var codeIDs []uint
		if err := tx.Model(&integral.SysPointsCode{}).Where("batch_id = ?", batchID).Pluck("id", &codeIDs).Error; err != nil {
			return err
		}

		if len(codeIDs) > 0 {
			// 删除使用记录
			if err := tx.Where("code_id IN ?", codeIDs).Delete(&integral.SysPointsCodeUsage{}).Error; err != nil {
				return err
			}
		}

		// 删除兑换码
		return tx.Where("batch_id = ?", batchID).Delete(&integral.SysPointsCode{}).Error
	})
}
